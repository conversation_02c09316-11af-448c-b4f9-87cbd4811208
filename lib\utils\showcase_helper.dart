import 'package:shared_preferences/shared_preferences.dart';

/// Helper class for managing showcase tours
class ShowcaseHelper {
  // SharedPreferences keys for showcase completion
  static const String homeShowcaseKey = 'home_showcase_completed';
  static const String profileShowcaseKey = 'profile_showcase_completed';
  static const String settingsShowcaseKey = 'settings_showcase_completed';

  /// Reset all showcase tours (useful for testing)
  static Future<void> resetAllShowcases() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(homeShowcaseKey);
    await prefs.remove(profileShowcaseKey);
    await prefs.remove(settingsShowcaseKey);
  }

  /// Reset specific showcase tour
  static Future<void> resetShowcase(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  /// Check if a showcase has been completed
  static Future<bool> isShowcaseCompleted(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? false;
  }

  /// Mark a showcase as completed
  static Future<void> markShowcaseCompleted(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, true);
  }

  /// Get status of all showcases
  static Future<Map<String, bool>> getAllShowcaseStatus() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'home': prefs.getBool(homeShowcaseKey) ?? false,
      'profile': prefs.getBool(profileShowcaseKey) ?? false,
      'settings': prefs.getBool(settingsShowcaseKey) ?? false,
    };
  }
}
