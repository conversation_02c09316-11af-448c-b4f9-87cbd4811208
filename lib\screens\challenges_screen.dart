import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_state.dart';
import '../theme/app_theme.dart';
import '../utils/persian_utils.dart';

class ChallengesScreen extends StatelessWidget {
  const ChallengesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the screen size to ensure proper layout
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: screenSize.width,
        height: screenSize.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF87CEEB), Colors.white],
          ),
        ),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAppBar(context),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  'چالش‌ها',
                  style: const TextStyle(
                    fontFamily: 'Samim',
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2F4F4F),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  'با تکمیل این چالش‌ها، کلی جایزه بگیر و رشد کن!',
                  style: const TextStyle(
                    fontFamily: 'Samim',
                    fontSize: 16,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: _buildChallengesList(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'نفس‌یار',
            style: GoogleFonts.lato(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(26), // 0.1 * 255 ≈ 26
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const Icon(
                  FeatherIcons.award,
                  size: 16,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 4),
                Text(
                  'چالش‌ها',
                  style: const TextStyle(
                    fontFamily: 'Samim',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChallengesList(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    // Get all challenge IDs
    final allChallenges = appState.challengeRequirements.keys.toList();

    // If there are no challenges
    if (allChallenges.isEmpty) {
      return _buildEmptyChallengeState(context);
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: allChallenges.length,
      itemBuilder: (context, index) {
        final challengeId = allChallenges[index];
        final challengeName =
            appState.challengeDescriptions[challengeId] ?? 'Unknown Challenge';
        final progress = appState.getChallengeProgressSync(challengeId);
        final isCompleted = progress['completed'] as bool;
        final currentProgress = progress['progress'] as int;
        final target = progress['target'] as int;

        return ChallengeCard(
          challengeId: challengeId,
          title: challengeName,
          description: _getChallengeDescription(challengeId),
          icon: _getChallengeIcon(challengeId),
          progress: currentProgress,
          target: target,
          isCompleted: isCompleted,
          progressLabel: _getChallengeProgressLabel(challengeId),
        );
      },
    );
  }

  Widget _buildEmptyChallengeState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TweenAnimationBuilder<double>(
              tween: Tween(begin: 0.0, end: 1.0),
              duration: const Duration(milliseconds: 800),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: value,
                  child: const Icon(
                    FeatherIcons.flag,
                    size: 64,
                    color: AppTheme.primaryColor,
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
            Text(
              'سفر چالش‌های شما در انتظار است!',
              style: GoogleFonts.lato(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2F4F4F),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'تمرینات تنفسی را تکمیل کنید تا چالش‌های هیجان‌انگیز را باز کرده و جوایز کسب کنید',
              style: TextStyle(
                fontFamily: 'Samim',
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate to HomeScreen (first tab)
                DefaultTabController.of(context).animateTo(0);
              },
              icon: const Icon(FeatherIcons.play, size: 18),
              label: Text(
                'شروع تمرین تنفسی',
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getChallengeDescription(String challengeId) {
    switch (challengeId) {
      case 'streak_7':
        return 'تمرینات تنفسی را به مدت ۷ روز متوالی تکمیل کنید';
      case 'streak_30':
        return 'تمرینات تنفسی را به مدت ۳۰ روز متوالی تکمیل کنید';
      case 'all_styles':
        return 'همه سبک‌های انیمیشن را باز کنید';
      case 'all_exercises':
        return 'همه تکنیک‌های تنفسی را حداقل یک بار امتحان کنید';
      case 'exercises_10':
        return '۱۰ تمرین تنفسی تکمیل کنید';
      case 'exercises_50':
        return '۵۰ تمرین تنفسی تکمیل کنید';
      case 'level_2':
        return 'به سطح ۲ برسید';
      default:
        return 'این چالش را تکمیل کنید تا جوایز کسب کنید';
    }
  }

  IconData _getChallengeIcon(String challengeId) {
    switch (challengeId) {
      case 'streak_7':
      case 'streak_30':
        return FeatherIcons.calendar;
      case 'all_styles':
        return FeatherIcons.eye;
      case 'all_exercises':
        return FeatherIcons.checkCircle;
      case 'exercises_10':
      case 'exercises_50':
        return FeatherIcons.activity;
      case 'level_2':
        return FeatherIcons.award;
      default:
        return FeatherIcons.flag;
    }
  }

  String _getChallengeProgressLabel(String challengeId) {
    switch (challengeId) {
      case 'streak_7':
      case 'streak_30':
        return 'روز';
      case 'all_exercises':
        return 'تکنیک‌های امتحان شده';
      case 'exercises_10':
      case 'exercises_50':
        return 'تمرین';
      case 'level_2':
        return 'سطح';
      case 'all_styles':
        return 'سبک';
      default:
        return '';
    }
  }
}

// Separate StatefulWidget for animated challenge cards
class ChallengeCard extends StatefulWidget {
  final String challengeId;
  final String title;
  final String description;
  final IconData icon;
  final int progress;
  final int target;
  final bool isCompleted;
  final String progressLabel;

  const ChallengeCard({
    super.key,
    required this.challengeId,
    required this.title,
    required this.description,
    required this.icon,
    required this.progress,
    required this.target,
    required this.isCompleted,
    required this.progressLabel,
  });

  @override
  State<ChallengeCard> createState() => _ChallengeCardState();
}

class _ChallengeCardState extends State<ChallengeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _previousCompletionStatus = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.5, curve: Curves.elasticOut),
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 1.0, curve: Curves.easeInOut),
    ));

    _previousCompletionStatus = widget.isCompleted;
  }

  @override
  void didUpdateWidget(ChallengeCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if challenge was just completed (transition from false to true)
    if (!_previousCompletionStatus && widget.isCompleted) {
      // Check if this is a minor challenge that should animate
      final minorChallenges = [
        'daily_challenge',
        'weekly_challenge',
        'exercises_10',
        'points_250'
      ];
      if (minorChallenges.contains(widget.challengeId)) {
        _animationController.forward(from: 0.0);
      }
    }
    _previousCompletionStatus = widget.isCompleted;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double percentage = widget.target > 0
        ? (widget.progress / widget.target).clamp(0.0, 1.0)
        : 0.0;
    final appState = Provider.of<AppState>(context, listen: false);
    final rewardPoints = appState.getChallengeReward(widget.challengeId);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
              // Add animated glow effect for minor challenges
              if (_glowAnimation.value > 0)
                BoxShadow(
                  color: AppTheme.primaryColor.withAlpha(
                    (0.3 * _glowAnimation.value * 255).toInt(),
                  ),
                  blurRadius: 20 * _glowAnimation.value,
                  offset: const Offset(0, 0),
                ),
            ],
            border: _glowAnimation.value > 0
                ? Border.all(
                    color: AppTheme.primaryColor.withAlpha(
                      (0.5 * _glowAnimation.value * 255).toInt(),
                    ),
                    width: 2 * _glowAnimation.value,
                  )
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: widget.isCompleted
                            ? Colors.green.withAlpha(51) // 0.2 * 255 ≈ 51
                            : AppTheme.primaryColor
                                .withAlpha(26), // 0.1 * 255 ≈ 26
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        widget.icon,
                        size: 24,
                        color: widget.isCompleted
                            ? Colors.green
                            : AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.title,
                            style: GoogleFonts.lato(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.description,
                            style: GoogleFonts.lato(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (!widget.isCompleted)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor
                              .withAlpha(26), // 0.1 * 255 ≈ 26
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              FeatherIcons.star,
                              size: 12,
                              color: Colors.amber,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${PersianUtils.toPersianNumber(rewardPoints)} امتیاز',
                              style: GoogleFonts.lato(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (widget.isCompleted)
                      ScaleTransition(
                        scale: _scaleAnimation,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.withAlpha(26), // 0.1 * 255 ≈ 26
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            FeatherIcons.check,
                            size: 16,
                            color: Colors.green,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: LinearProgressIndicator(
                          value: percentage,
                          backgroundColor:
                              Colors.grey.withAlpha(26), // 0.1 * 255 ≈ 26
                          minHeight: 8,
                          color: widget.isCompleted
                              ? Colors.green
                              : AppTheme.primaryColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          widget.target > 0
                              ? '${PersianUtils.toPersianNumber(widget.progress)}/${PersianUtils.toPersianNumber(widget.target)}'
                              : 'در حال انجام',
                          style: GoogleFonts.lato(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: widget.isCompleted
                                ? Colors.green
                                : Colors.grey[600],
                          ),
                        ),
                        Text(
                          widget.progressLabel,
                          style: GoogleFonts.lato(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                if (widget.isCompleted) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Icon(
                        FeatherIcons.award,
                        size: 16,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'جایزه: ${PersianUtils.toPersianNumber(rewardPoints)} امتیاز',
                        style: GoogleFonts.lato(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.amber[700],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
