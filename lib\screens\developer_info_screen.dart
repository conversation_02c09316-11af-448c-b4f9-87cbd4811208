import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:url_launcher/url_launcher.dart';
import '../theme/app_theme.dart';
import 'contact_form_screen.dart';

class DeveloperInfoScreen extends StatelessWidget {
  const DeveloperInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.textColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'درباره نفس‌یار و من',
          style: TextStyle(
            fontFamily: 'Samim',
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textColor,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        color: const Color(0xFFF8F9FA),
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 20),
              _buildPersonalMessageSection(),
              const SizedBox(height: 20),
              _buildSupportSection(context),
              const SizedBox(height: 20),
              _buildContactSection(context),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalMessageSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
            child: Icon(
              FeatherIcons.code,
              size: 40,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'سلام، من حسن دائمی توسعه‌دهنده نفس‌یار هستم',
            style: TextStyle(
              fontFamily: 'Samim',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Text(
            'نفس‌یار را با عشق و دقت برای کمک به بهبود سلامت روانی و آرامش ذهنی شما ساخته‌ام. هدف من این است که ابزاری ساده و مؤثر در اختیار شما قرار دهم تا بتوانید در زندگی روزمره‌تان لحظاتی از آرامش و تمرکز را تجربه کنید.\n\nامیدوارم این اپلیکیشن بتواند همراه مفیدی برای شما باشد و به بهبود کیفیت زندگی‌تان کمک کند.',
            style: TextStyle(
              fontFamily: 'Samim',
              fontSize: 16,
              height: 1.8,
              color: AppTheme.lightTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSupportSection(BuildContext context) {
    return _buildSection(
      icon: FeatherIcons.gift,
      iconColor: Colors.red.shade400,
      title: 'از پروژه حمایت کنید',
      content:
          'اگر نفس‌یار برای شما مفید بوده و می‌خواهید از ادامه توسعه این پروژه حمایت کنید، می‌توانید از طریق لینک زیر هدیه‌ای برای من ارسال کنید. حمایت شما انگیزه بزرگی برای ادامه کار و بهبود اپلیکیشن است.',
      buttonText: 'پرداخت هدیه (درگاه امن)',
      onButtonPressed: () => _launchURL('https://coffeete.ir/Hassan_daemi'),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return _buildSection(
      icon: FeatherIcons.mail,
      iconColor: AppTheme.primaryColor,
      title: 'نظرات شما را می‌شنوم',
      content:
          'نظرات، پیشنهادات و انتقادات شما برای من بسیار ارزشمند است. اگر ایده‌ای برای بهبود اپلیکیشن دارید یا با مشکلی مواجه شده‌اید، خوشحال می‌شوم از شما بشنوم.',
      buttonText: 'ارسال پیام به توسعه‌دهنده',
      onButtonPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const ContactFormScreen(),
          ),
        );
      },
    );
  }

  Widget _buildSection({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String content,
    required String buttonText,
    required VoidCallback onButtonPressed,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: iconColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            content,
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 15,
              height: 1.7,
              color: AppTheme.lightTextColor,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onButtonPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                buttonText,
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        debugPrint('Could not launch $url');
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }
}
