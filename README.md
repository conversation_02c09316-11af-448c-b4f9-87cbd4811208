# 🌬️ NafasYar (نفس‌یار) - Persian Breathing Exercise App

**NafasYar** is a comprehensive Persian breathing exercise application designed to help users achieve relaxation, reduce stress, and improve their overall well-being through guided breathing techniques, gamification, and progress tracking.

## 🌟 Features

### 🏠 Home Screen
- **Progressive Unlocking System**: Breathing techniques unlock as you level up
- **6 Breathing Techniques** with unique patterns and benefits:
  - **Pursed Lip Breathing** (Available from start) - Improves breathing efficiency
  - **4-7-8 Breathing** (Unlocks at Calm Seeker) - Promotes relaxation and sleep
  - **Box Breathing** (Unlocks at Calm Seeker) - Enhances focus and reduces stress
  - **<PERSON><PERSON><PERSON><PERSON>** (Unlocks at Breath Master) - Relieves tension with humming
  - **<PERSON><PERSON><PERSON>** (Unlocks at Breath Legend) - Energizing forceful breathing
- **Responsive Design** that works on all device sizes
- **Visual Breathing Animations** with dynamic color transitions and haptic feedback

### 🎵 Music Player
- **4 Background Tracks** for enhanced relaxation:
  - Calm Lake
  - Easy
  - Heaven
  - Soul
- **Offline Caching** - Download tracks for use without internet
- **Seamless Integration** with breathing exercises
- **Music Controls** - Play, pause, and track selection during exercises
- **Graceful Offline Handling** with retry options

### 🏆 Gamification & Progress
- **Point System**:
  - First daily exercise: 15 points
  - Second daily exercise: 10 points
  - Third daily exercise: 5 points
  - Challenge completion: 20-100 points based on difficulty
- **User Progression Levels**:
  - **Breath Novice** (0-99 points) - Starting level
  - **Calm Seeker** (100+ points) - Unlocks 4-7-8 and Box Breathing
  - **Breath Master** (250+ points + 2 challenges) - Unlocks Bhramari Pranayama
  - **Breath Legend** (500+ points + 4 challenges) - Unlocks Kapal Bhati Pranayama

### 🎯 Challenges
- **Daily Challenges**: Complete one breathing exercise
- **Weekly Challenges**: Complete 5 exercises in a week
- **Streak Challenges**: 7-day and 30-day consecutive streaks
- **Milestone Challenges**: Complete 10 or 50 total exercises
- **Level Challenges**: Reach specific user levels
- **Exercise Explorer**: Try all available breathing techniques

### 👤 Profile & Analytics
- **Mood Tracking**: Rate your mood after each exercise
- **Mood Analysis Charts** with emoji representations (😊, 😌, 😐, 😓, 😡)
- **Progress Visualization**: Track your improvement over time
- **User Statistics**: Points, level, completed challenges, and streaks
- **Profile Customization**: Choose from 4 different profile pictures

### 🔔 Smart Notifications
- **Daily Reminders**: Customizable notification times
- **Permission Management**: Seamless notification setup
- **Motivational Messages**: Encouraging daily practice

### 🎨 User Experience
- **4-Page Onboarding Flow** (first launch only):
  1. Welcome & Benefits
  2. Breathing Techniques Overview
  3. Progress Tracking Features
  4. Personalization Options
- **Bottom Navigation**: Easy access to Home, Challenges, and Profile
- **Responsive Design**: Optimized for all screen sizes
- **Smooth Animations**: Enhanced visual feedback throughout the app

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (^3.6.0)
- Dart SDK (^3.6.0)
- Android Studio / VS Code with Flutter extensions
- Android device/emulator or iOS device/simulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nafasyar
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

### Platform-Specific Setup

#### Android
- Minimum SDK version: 21
- Target SDK version: 34
- Permissions: Internet, Vibration, Notifications

#### iOS
- Minimum iOS version: 12.0
- Permissions: Notifications

## 🛠 Technologies Used

- **Flutter** - Cross-platform mobile development framework
- **Provider** - State management solution
- **SQLite** - Local database for exercise history and mood tracking
- **SharedPreferences** - Persistent app settings and user preferences
- **AudioPlayers** - Background music playback
- **Flutter Local Notifications** - Daily reminder notifications
- **Vibration** - Haptic feedback during breathing exercises
- **HTTP** - Music track downloading and caching
- **Google Fonts** - Typography (Lato font family)
- **Flutter Feather Icons** - Consistent iconography

## 📱 App Structure

```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
│   ├── breathing_technique.dart
│   ├── music_track.dart
│   └── exercise_history.dart
├── providers/                # State management
│   └── app_state.dart
├── screens/                  # UI screens
│   ├── main_navigation.dart
│   ├── home_screen.dart
│   ├── breathing_screen.dart
│   ├── challenges_screen.dart
│   ├── profile_screen.dart
│   ├── settings_screen.dart
│   └── onboarding_screen.dart
├── widgets/                  # Reusable components
│   ├── music_player_widget.dart
│   └── mood_dialog.dart
├── database/                 # Local storage
│   └── database_helper.dart
└── theme/                    # App styling
    └── app_theme.dart
```

## 🎯 How to Use

1. **First Launch**: Complete the 4-page onboarding to learn about features
2. **Choose a Technique**: Start with Pursed Lip Breathing (always available)
3. **Follow the Animation**: Breathe in sync with the visual guide
4. **Track Your Mood**: Rate how you feel after each session
5. **Earn Points**: Complete exercises to gain points and unlock new techniques
6. **Complete Challenges**: Work towards daily, weekly, and milestone goals
7. **Level Up**: Progress through Breath Novice → Calm Seeker → Breath Master → Breath Legend
8. **Customize**: Set notification times, choose background music, and personalize your profile

## 🔧 Configuration

### Notifications
- Enable in Settings → Notifications
- Set custom reminder times
- Requires system notification permissions

### Music
- Enable in Settings → Background Music
- Choose from 4 available tracks
- Download tracks for offline use
- Music continues during breathing exercises

### Profile
- Select from 4 profile pictures
- Track your progress and achievements
- View mood analysis and trends

## 📊 Progress Tracking

The app tracks multiple metrics to help you monitor your breathing practice:

- **Daily Streak**: Consecutive days with at least one exercise
- **Total Exercises**: Lifetime count of completed sessions
- **Points Earned**: Accumulated through exercises and challenges
- **Mood Trends**: Visual analysis of your emotional wellbeing
- **Challenge Progress**: Track completion of various goals
- **Technique Mastery**: Monitor usage of different breathing patterns

## 🤝 Contributing

This is a personal wellness app project. If you'd like to contribute or report issues, please follow standard Flutter development practices and ensure all changes maintain the app's focus on user wellbeing and accessibility.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Background music tracks provided via jsDelivr CDN
- Flutter community for excellent packages and documentation
- Breathing technique research from various wellness and mindfulness sources
