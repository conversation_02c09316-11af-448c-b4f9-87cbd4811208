import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import 'main_navigation.dart';
import '../providers/app_state.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController(
    viewportFraction: 1.0,
    initialPage: 0,
  );
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_completed', true);

    if (mounted) {
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              MainNavigation(key: mainNavigationKey),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  void _nextPage() {
    if (_currentPage < 4) {
      _pageController.animateToPage(
        _currentPage + 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF87CEEB), Colors.white],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Skip button
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: TextButton(
                    onPressed: _skipOnboarding,
                    child: Text(
                      'رد کردن',
                      style: GoogleFonts.lato(
                        fontSize: 16,
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),

              // Page content
              Expanded(
                child: Directionality(
                  textDirection: TextDirection.rtl, // Keep RTL for content
                  child: PageView(
                    controller: _pageController,
                    scrollDirection: Axis.horizontal,
                    reverse:
                        true, // This makes the PageView swipe left-to-right
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                    },
                    children: [
                      _buildPage1(),
                      _buildPage2(),
                      _buildPage3(),
                      _buildPage4(),
                      _buildPage5(),
                    ],
                  ),
                ),
              ),

              // Page indicators (RTL layout - reversed order)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 20.0),
                child: Directionality(
                  textDirection: TextDirection.ltr, // Force LTR for the row
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      5,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4.0),
                        width: _currentPage == (4 - index) ? 12.0 : 8.0,
                        height: 8.0,
                        decoration: BoxDecoration(
                          color: _currentPage == (4 - index)
                              ? AppTheme.primaryColor
                              : AppTheme.primaryColor
                                  .withAlpha((0.3 * 255).toInt()),
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                      ),
                    ).reversed.toList(), // Reverse the list to show dots RTL
                  ),
                ),
              ),

              // Navigation button
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _nextPage,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      _currentPage == 4 ? 'شروع کنیم!' : 'بعدی',
                      style: GoogleFonts.lato(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Page 1 - Welcome & Core Benefit
  Widget _buildPage1() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: App logo
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
              shape: BoxShape.circle,
              border: Border.all(
                color: AppTheme.primaryColor,
                width: 3,
              ),
            ),
            child: ClipOval(
              child: Padding(
                padding: const EdgeInsets.all(30.0),
                child: Image.asset(
                  'assets/images/icon/icon_app.webp',
                  width: 140,
                  height: 140,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(
                      FeatherIcons.wind,
                      size: 100,
                      color: AppTheme.primaryColor,
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 60),

          // Title
          Text(
            'به دنیای آرامش با نفس‌یار قدم بذار!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'با چندتا نفس ساده، می‌تونی استرس روزانه‌ات رو مثل آب خوردن کم کنی، تمرکزت رو چند برابر کنی و حال دلت رو حسابی خوب کنی. نفس‌یار اومده تا این مسیر رو برات شیرین‌تر کنه.',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Page 2 - Breathing Techniques
  Widget _buildPage2() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: Three technique icons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildTechniqueIcon(
                FeatherIcons.moon,
                Colors.indigo,
                'خواب',
              ),
              _buildTechniqueIcon(
                FeatherIcons.zap,
                Colors.orange,
                'انرژی',
              ),
              _buildTechniqueIcon(
                FeatherIcons.target,
                Colors.teal,
                'تمرکز',
              ),
            ],
          ),
          const SizedBox(height: 60),

          // Title
          Text(
            'هر موقعیتی، یه جور نفس!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'چه دنبال یه خواب آروم باشی، چه انرژی اول صبح، یا تمرکز وسط شلوغی کار، نفس‌یار یه تکنیک تنفس مخصوص خودت رو داره. هرچی بیشتر با ما همراه باشی، تکنیک‌های خفن‌تری هم برات رو می‌کنیم!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper method for technique icons
  Widget _buildTechniqueIcon(IconData icon, Color color, String label) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: color.withAlpha((0.2 * 255).toInt()),
            shape: BoxShape.circle,
            border: Border.all(
              color: color,
              width: 2,
            ),
          ),
          child: Icon(
            icon,
            size: 40,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: GoogleFonts.lato(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textColor,
          ),
        ),
      ],
    );
  }

  // Page 3 - Progress Tracking
  Widget _buildPage3() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: Progress icons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildProgressIcon(
                FeatherIcons.star,
                AppTheme.primaryColor,
                'امتیازها',
              ),
              _buildProgressIcon(
                FeatherIcons.trendingUp,
                Colors.green,
                'پیشرفت',
              ),
              _buildProgressIcon(
                FeatherIcons.award,
                Colors.amber,
                'دستاوردها',
              ),
            ],
          ),
          const SizedBox(height: 40),

          // Sample chart preview
          _buildSampleChart(),
          const SizedBox(height: 40),

          // Title
          Text(
            'سفر آرامشت رو جذاب‌تر کن!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'با هر تمرین، امتیاز جمع کن، عنوان‌های باحال بگیر و چالش‌ها رو یکی یکی فتح کن. هر روز ببین چقدر حالت بهتر می‌شه و چطور داری استاد آرامش خودت می‌شی!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper method for progress icons
  Widget _buildProgressIcon(IconData icon, Color color, String label) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: color.withAlpha((0.2 * 255).toInt()),
            shape: BoxShape.circle,
            border: Border.all(
              color: color,
              width: 2,
            ),
          ),
          child: Icon(
            icon,
            size: 40,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: GoogleFonts.lato(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textColor,
          ),
        ),
      ],
    );
  }

  // Page 4 - Personalization
  Widget _buildPage4() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: All profile avatars showcase
          Column(
            children: [
              // Top row of profiles
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildProfileAvatar(
                      'assets/images/profiles/profile_level1.webp', 'مبتدی'),
                  _buildProfileAvatar(
                      'assets/images/profiles/profile_level2.webp', 'متوسط'),
                ],
              ),
              const SizedBox(height: 20),
              // Bottom row of profiles
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildProfileAvatar(
                      'assets/images/profiles/profile_level3.webp', 'پیشرفته'),
                  _buildProfileAvatar(
                      'assets/images/profiles/profile_level4.webp', 'استاد'),
                ],
              ),
            ],
          ),
          const SizedBox(height: 60),

          // Title
          Text(
            'نفس‌یار رو اونجوری که دوست داری بچین!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'موسیقی پس‌زمینه‌ای که بهت آرامش می‌ده رو انتخاب کن، یادآور تنظیم کن تا تمرین‌هات یادت نره، و با گرفتن عنوان‌های جدید، کلی آواتار خوشگل برای پروفایلت آزاد کن.',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper method for profile avatars
  Widget _buildProfileAvatar(String imagePath, String label) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
            shape: BoxShape.circle,
            border: Border.all(
              color: AppTheme.primaryColor,
              width: 2,
            ),
          ),
          child: ClipOval(
            child: Image.asset(
              imagePath,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  FeatherIcons.user,
                  size: 40,
                  color: AppTheme.primaryColor,
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Samim',
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppTheme.textColor,
          ),
        ),
      ],
    );
  }

  // Helper method for sample chart
  Widget _buildSampleChart() {
    return Container(
      height: 120,
      width: 280,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.1 * 255).toInt()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'سفر آرامش شما در این هفته',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: 5,
                minY: 1,
                titlesData: const FlTitlesData(show: false),
                gridData: const FlGridData(show: false),
                borderData: FlBorderData(show: false),
                barGroups: [
                  BarChartGroupData(
                    x: 0,
                    barRods: [
                      BarChartRodData(
                        toY: 3.0,
                        color: AppTheme.primaryColor
                            .withAlpha((0.6 * 255).toInt()),
                        width: 20,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 1,
                    barRods: [
                      BarChartRodData(
                        toY: 3.5,
                        color: AppTheme.primaryColor
                            .withAlpha((0.6 * 255).toInt()),
                        width: 20,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 2,
                    barRods: [
                      BarChartRodData(
                        toY: 3.2,
                        color: AppTheme.primaryColor
                            .withAlpha((0.6 * 255).toInt()),
                        width: 20,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 3,
                    barRods: [
                      BarChartRodData(
                        toY: 4.1,
                        color: AppTheme.primaryColor
                            .withAlpha((0.6 * 255).toInt()),
                        width: 20,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 4,
                    barRods: [
                      BarChartRodData(
                        toY: 4.5,
                        color: AppTheme.primaryColor
                            .withAlpha((0.6 * 255).toInt()),
                        width: 20,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 5,
                    barRods: [
                      BarChartRodData(
                        toY: 4.2,
                        color: AppTheme.primaryColor
                            .withAlpha((0.6 * 255).toInt()),
                        width: 20,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 6,
                    barRods: [
                      BarChartRodData(
                        toY: 4.8,
                        color: AppTheme.primaryColor
                            .withAlpha((0.6 * 255).toInt()),
                        width: 20,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Page 5 - Notification Permission
  Widget _buildPage5() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Visual: Notification icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
              shape: BoxShape.circle,
              border: Border.all(
                color: AppTheme.primaryColor,
                width: 3,
              ),
            ),
            child: const Icon(
              FeatherIcons.bell,
              size: 60,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 60),

          // Title
          Text(
            'همراه همیشگی آرامشت!',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Description
          Text(
            'نذار تمرین روزانه‌ات یادت بره! با فعال کردن یادآور، نفس‌یار مثل یه دوست خوب، به موقع بهت خبر می‌ده تا همیشه تو مسیر آرامش بمونی و یه عادت فوق‌العاده بسازی.',
            style: const TextStyle(
              fontFamily: 'Samim',
              fontSize: 18,
              color: AppTheme.lightTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),

          // Permission request button
          Consumer<AppState>(
            builder: (context, appState, child) {
              return Column(
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      final scaffoldMessenger = ScaffoldMessenger.of(context);
                      final success =
                          await appState.requestNotificationPermissions();
                      if (success) {
                        // Permission granted, show success message
                        if (mounted) {
                          scaffoldMessenger.showSnackBar(
                            const SnackBar(
                              content: Text(
                                'عالی! شما یادآوری‌های مفید دریافت خواهید کرد.',
                                style: TextStyle(fontFamily: 'Samim'),
                              ),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        }
                      }
                      // Continue to next step regardless of permission result
                      await Future.delayed(const Duration(milliseconds: 500));
                      _completeOnboarding();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: Text(
                      'فعال‌سازی یادآوری‌ها',
                      style: const TextStyle(
                        fontFamily: 'Samim',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: _completeOnboarding,
                    child: Text(
                      'شاید بعداً',
                      style: const TextStyle(
                        fontFamily: 'Samim',
                        fontSize: 14,
                        color: AppTheme.lightTextColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
