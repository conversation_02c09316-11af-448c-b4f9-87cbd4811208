import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:provider/provider.dart';
import '../providers/app_state.dart';
import '../utils/persian_utils.dart';
import 'home_screen.dart';
import 'challenges_screen.dart';
import 'profile_screen.dart';

// Global key to access MainNavigation state from anywhere
final GlobalKey<MainNavigationState> mainNavigationKey =
    GlobalKey<MainNavigationState>();

// Static method to switch tabs from anywhere in the app
class NavigationHelper {
  static void switchToHomeTab() {
    mainNavigationKey.currentState?.switchToTab(0);
  }
}

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => MainNavigationState();
}

class MainNavigationState extends State<MainNavigation>
    with SingleTickerProviderStateMixin {
  int _currentIndex = 0;
  late AnimationController _animationController;
  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _screens = [
      const HomeScreen(),
      const ChallengesScreen(),
      const ProfileScreen(),
    ];

    // Start the animation immediately when the app loads
    _animationController.forward();

    // Add listener for significant challenge completions
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = Provider.of<AppState>(context, listen: false);
      appState.addListener(_onAppStateChanged);
    });
  }

  void _onAppStateChanged() {
    final appState = Provider.of<AppState>(context, listen: false);
    final significantChallenge = appState.newlyCompletedSignificantChallenge;

    if (significantChallenge != null) {
      _showSignificantChallengeSnackBar(significantChallenge);
      appState.clearSignificantChallengeFlag();
    }
  }

  void _showSignificantChallengeSnackBar(Map<String, dynamic> challengeData) {
    final challengeName = challengeData['challengeName'] as String;
    final pointsAwarded = challengeData['pointsAwarded'] as int;
    final persianPoints = PersianUtils.toPersianNumber(pointsAwarded);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.amber.withAlpha(51), // 0.2 * 255 ≈ 51
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  FeatherIcons.award,
                  color: Colors.amber,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'چالش تکمیل شد!',
                      style: const TextStyle(
                        fontFamily: 'Samim',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '$challengeName - $persianPoints امتیاز',
                      style: const TextStyle(
                        fontFamily: 'Samim',
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        backgroundColor: const Color(0xFF4682B4), // App primary color
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
        elevation: 6,
      ),
    );
  }

  @override
  void dispose() {
    final appState = Provider.of<AppState>(context, listen: false);
    appState.removeListener(_onAppStateChanged);
    _animationController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    if (_currentIndex == index) return;

    _animationController.forward(from: 0.0);
    setState(() {
      _currentIndex = index;
    });
  }

  // Public method to switch tabs from external widgets
  void switchToTab(int index) {
    _onTabTapped(index);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: Tween<double>(begin: 0.8, end: 1.0).animate(
              CurvedAnimation(
                parent: _animationController,
                curve: Curves.easeInOut,
              ),
            ),
            child: ScaleTransition(
              scale: Tween<double>(begin: 0.95, end: 1.0).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: Curves.easeInOut,
                ),
              ),
              child: _screens[_currentIndex],
            ),
          );
        },
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        backgroundColor: Colors.white,
        selectedItemColor: const Color(0xFF4682B4), // Steel Blue as requested
        unselectedItemColor: const Color(0xFF808080), // Gray as requested
        selectedLabelStyle: const TextStyle(fontWeight: FontWeight.w600),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(FeatherIcons.home),
            label: 'خانه',
          ),
          BottomNavigationBarItem(
            icon: Icon(FeatherIcons.flag),
            label: 'چالش‌ها',
          ),
          BottomNavigationBarItem(
            icon: Icon(FeatherIcons.user),
            label: 'پروفایل',
          ),
        ],
        elevation: 8,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}
