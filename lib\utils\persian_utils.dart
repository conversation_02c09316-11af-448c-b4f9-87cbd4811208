/// Utility functions for Persian localization
class PersianUtils {
  /// Map of English digits to Persian digits
  static const Map<String, String> _digitMap = {
    '0': '۰',
    '1': '۱',
    '2': '۲',
    '3': '۳',
    '4': '۴',
    '5': '۵',
    '6': '۶',
    '7': '۷',
    '8': '۸',
    '9': '۹',
  };

  /// Convert English numerals to Persian numerals
  static String toPersianNumbers(String input) {
    String result = input;
    _digitMap.forEach((english, persian) {
      result = result.replaceAll(english, persian);
    });
    return result;
  }

  /// Convert integer to Persian numerals
  static String toPersianNumber(int number) {
    return toPersianNumbers(number.toString());
  }

  /// Convert double to Persian numerals
  static String toPersianNumberDouble(double number) {
    return toPersianNumbers(number.toString());
  }

  /// Convert English numerals back to Persian (for parsing)
  static String toEnglishNumbers(String input) {
    String result = input;
    _digitMap.forEach((english, persian) {
      result = result.replaceAll(persian, english);
    });
    return result;
  }
}
